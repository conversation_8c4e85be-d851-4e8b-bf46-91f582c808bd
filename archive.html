<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Sky Senderos - Archived Surveys</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      min-height: 100vh;
      color: #333;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }

    h1 {
      text-align: center;
      color: #585c2c;
      font-size: 2.5rem;
      margin-bottom: 30px;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
      font-weight: 700;
    }

    .search-section {
      background: white;
      border-radius: 15px;
      padding: 30px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.1);
      margin-bottom: 30px;
    }

    .search-form {
      display: flex;
      gap: 15px;
      align-items: center;
      flex-wrap: wrap;
    }

    .search-form input {
      flex: 1;
      min-width: 250px;
      padding: 12px 15px;
      border: 2px solid #e0e0e0;
      border-radius: 8px;
      font-size: 16px;
      transition: all 0.3s ease;
    }

    .search-form input:focus {
      outline: none;
      border-color: #585c2c;
      box-shadow: 0 0 0 3px rgba(88, 92, 44, 0.1);
    }

    .search-form button {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      background: linear-gradient(45deg, #585c2c, #6b7030);
      color: white;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(88, 92, 44, 0.3);
    }

    .search-form button:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(88, 92, 44, 0.4);
      background: linear-gradient(45deg, #6b7030, #787c34);
    }

    .clear-search {
      background: linear-gradient(45deg, #6c757d, #5a6268);
      box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
    }

    .clear-search:hover {
      background: linear-gradient(45deg, #5a6268, #545b62);
      box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
    }

    .results-info {
      text-align: center;
      margin: 20px 0;
      color: #585c2c;
      font-weight: 600;
    }

    .search-summary {
      background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
      padding: 15px 20px;
      border-radius: 10px;
      margin-bottom: 20px;
      border-left: 4px solid #585c2c;
    }

    #surveys-container {
      display: flex;
      flex-direction: column;
      gap: 25px;
    }

    .survey-card {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      border: 2px solid #e0e0e0;
      padding: 25px;
      border-radius: 15px;
      background: linear-gradient(135deg, #ffffff, #f8f9fa);
      box-shadow: 0 8px 25px rgba(0,0,0,0.1);
      transition: all 0.3s ease;
      opacity: 0;
      transform: translateY(20px);
      animation: slideInUp 0.5s ease forwards;
    }

    .survey-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 12px 35px rgba(0,0,0,0.15);
      border-color: #585c2c;
    }

    @keyframes slideInUp {
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .survey-info {
      flex: 1 1 70%;
      line-height: 1.6;
    }

    .survey-info p {
      margin: 8px 0;
      font-size: 14px;
    }

    .survey-info strong {
      color: #585c2c;
      font-weight: 600;
    }

    .archived-badge {
      display: inline-block;
      background: linear-gradient(45deg, #dc3545, #c82333);
      color: white;
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;
      margin-left: 10px;
    }

    .survey-attachments {
      flex: 0 0 30%;
      display: flex;
      flex-direction: column;
      gap: 15px;
      align-items: center;
      width: 300px;
    }

    .survey-image {
      width: 100%;
      display: flex;
      justify-content: center;
    }

    .survey-image img {
      max-width: 300px;
      max-height: 200px;
      border-radius: 12px;
      object-fit: contain;
      box-shadow: 0 8px 20px rgba(0,0,0,0.15);
      transition: transform 0.3s ease;
    }

    .survey-image img:hover {
      transform: scale(1.05);
    }

    .pdf-section {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 10px;
      width: 100%;
    }

    .pdf-viewer {
      width: 300px;
      height: 200px;
      border: 2px solid #e0e0e0;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 8px 20px rgba(0,0,0,0.15);
      transition: all 0.3s ease;
      background: #f8f9fa;
      cursor: pointer;
      position: relative;
    }

    .pdf-viewer:hover {
      transform: scale(1.02);
      border-color: #585c2c;
      box-shadow: 0 12px 30px rgba(0,0,0,0.2);
    }

    .pdf-viewer iframe {
      width: 100%;
      height: 100%;
      border: none;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
    }

    .pdf-error {
      width: 300px;
      height: 200px;
      border: 2px dashed #ccc;
      border-radius: 12px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: #f8f9fa;
      color: #666;
      text-align: center;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .pdf-error:hover {
      border-color: #585c2c;
      background: #f0f0f0;
    }

    .pdf-icon {
      width: 16px;
      height: 16px;
      background: white;
      border-radius: 3px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 8px;
      font-weight: bold;
      color: #dc3545;
    }

    .image-error {
      color: #dc3545;
      font-size: 12px;
      font-style: italic;
      margin-top: 10px;
      padding: 10px;
      background: #f8d7da;
      border: 1px solid #f5c6cb;
      border-radius: 8px;
    }

    .loading {
      text-align: center;
      color: #585c2c;
      margin: 40px 0;
      font-size: 18px;
      font-weight: 600;
    }

    .loading::after {
      content: '';
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 3px solid #585c2c;
      border-top: 3px solid transparent;
      border-radius: 50%;
      margin-left: 10px;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .no-results {
      text-align: center;
      color: #666;
      margin: 60px 0;
      font-style: italic;
      font-size: 18px;
      padding: 40px;
      background: linear-gradient(135deg, #f8f9fa, #e9ecef);
      border-radius: 15px;
      box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .end-of-results {
      text-align: center;
      color: #666;
      margin: 40px 0;
      padding: 20px;
      font-style: italic;
      border-top: 2px dashed #ccc;
    }

    .error-message {
      background: #f8d7da;
      color: #721c24;
      padding: 20px;
      border-radius: 10px;
      margin: 20px 0;
      border: 1px solid #f5c6cb;
      text-align: center;
    }

    /* PDF Modal for fullscreen viewing */
    .pdf-modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.9);
    }

    .pdf-modal-content {
      position: relative;
      margin: 2% auto;
      width: 95%;
      height: 95%;
      background: white;
      border-radius: 10px;
      overflow: hidden;
    }

    .pdf-modal-header {
      background: #585c2c;
      color: white;
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .pdf-modal-close {
      background: none;
      border: none;
      color: white;
      font-size: 28px;
      font-weight: bold;
      cursor: pointer;
      padding: 0;
      line-height: 1;
    }

    .pdf-modal-close:hover {
      opacity: 0.7;
    }

    .pdf-modal iframe {
      width: 100%;
      height: calc(100% - 60px);
      border: none;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .container {
        padding: 10px;
      }
      
      h1 {
        font-size: 2rem;
      }
      
      .search-form {
        flex-direction: column;
      }
      
      .search-form input {
        min-width: auto;
        width: 100%;
      }
      
      .search-form button {
        width: 100%;
      }
      
      .survey-card {
        flex-direction: column;
        gap: 20px;
      }
      
      .survey-attachments {
        align-self: center;
        width: 100%;
      }
      
      .survey-image img {
        max-width: 100%;
        max-height: 200px;
      }
    }

    /* Smooth scrolling */
    html {
      scroll-behavior: smooth;
    }

    /* Enhanced focus states for accessibility */
    *:focus {
      outline: 3px solid rgba(88, 92, 44, 0.5);
      outline-offset: 2px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>SkySenderos - Archived Surveys</h1>

    <div class="search-section">
      <div class="search-form">
        <input type="text" id="property-search" placeholder="Search by property name..." />
        <button onclick="searchArchives()">Search</button>
        <button class="clear-search" onclick="clearSearch()">Clear</button>
      </div>
    </div>

    <div id="search-summary" class="search-summary" style="display: none;"></div>
    <div id="results-info" class="results-info"></div>
    <div id="error-message" class="error-message" style="display: none;"></div>

    <div id="surveys-container"></div>
    
    <div id="loading" class="loading" style="display: none;">Loading archived surveys...</div>
    <div id="no-results" class="no-results" style="display: none;">
      No archived surveys found.
    </div>
    <div id="end-of-results" class="end-of-results" style="display: none;">
      End of archived surveys
    </div>
  </div>

  <script>
    const API_URL = 'https://zhtq9c7gge.execute-api.us-east-2.amazonaws.com/prod/surveys';
    const API_KEY = 'Sky193124141233';
    const PAGE_SIZE = 3;

    // State management
    let currentPage = 1;
    let isLoading = false;
    let hasMoreResults = true;
    let currentSearch = '';
    let totalResults = 0;
    let surveyDataStore = {};

    // Intersection Observer for infinite scroll
    let observer;

    // Initialize the page
    document.addEventListener('DOMContentLoaded', function() {
      setupInfiniteScroll();
      loadArchives();
      
      // Enter key search
      document.getElementById('property-search').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
          searchArchives();
        }
      });
    });

    function setupInfiniteScroll() {
      // Create a sentinel element at the bottom
      const sentinel = document.createElement('div');
      sentinel.id = 'scroll-sentinel';
      sentinel.style.height = '1px';
      document.body.appendChild(sentinel);

      // Set up intersection observer
      observer = new IntersectionObserver((entries) => {
        const entry = entries[0];
        if (entry.isIntersecting && !isLoading && hasMoreResults) {
          console.log('Intersection detected, loading more...');
          loadMoreArchives();
        }
      }, {
        root: null,
        rootMargin: '100px', // Load when 100px before reaching the sentinel
        threshold: 0
      });

      observer.observe(sentinel);
    }

    function searchArchives() {
      const searchValue = document.getElementById('property-search').value.trim();
      console.log('Searching for:', searchValue);
      
      currentSearch = searchValue;
      currentPage = 1;
      hasMoreResults = true;
      totalResults = 0;
      
      // Clear existing results
      document.getElementById('surveys-container').innerHTML = '';
      surveyDataStore = {};
      
      // Update UI
      updateSearchSummary();
      hideMessages();
      
      loadArchives();
    }

    function clearSearch() {
      document.getElementById('property-search').value = '';
      currentSearch = '';
      currentPage = 1;
      hasMoreResults = true;
      totalResults = 0;
      
      // Clear existing results
      document.getElementById('surveys-container').innerHTML = '';
      surveyDataStore = {};
      
      // Update UI
      updateSearchSummary();
      hideMessages();
      
      loadArchives();
    }

    function updateSearchSummary() {
      const summaryDiv = document.getElementById('search-summary');
      if (currentSearch) {
        summaryDiv.style.display = 'block';
        summaryDiv.innerHTML = `<strong>Searching for:</strong> "${currentSearch}"`;
      } else {
        summaryDiv.style.display = 'none';
      }
    }

    function hideMessages() {
      document.getElementById('no-results').style.display = 'none';
      document.getElementById('end-of-results').style.display = 'none';
      document.getElementById('error-message').style.display = 'none';
    }

    function showError(message) {
      const errorDiv = document.getElementById('error-message');
      errorDiv.textContent = message;
      errorDiv.style.display = 'block';
      document.getElementById('loading').style.display = 'none';
    }

    function setLoadingState(loading) {
      isLoading = loading;
      const loadingDiv = document.getElementById('loading');
      loadingDiv.style.display = loading ? 'block' : 'none';
    }

    function loadArchives() {
      currentPage = 1;
      loadMoreArchives();
    }

    async function loadMoreArchives() {
      if (isLoading || !hasMoreResults) {
        console.log('Skipping load - isLoading:', isLoading, 'hasMoreResults:', hasMoreResults);
        return;
      }

      console.log('Loading archives page:', currentPage, 'search:', currentSearch);
      setLoadingState(true);
      hideMessages();

      try {
        const url = new URL(API_URL);
        url.searchParams.append('archives', 'true'); // This tells the Lambda to use archived surveys
        url.searchParams.append('page', currentPage.toString());
        // Required parameters to satisfy the existing API validation
        url.searchParams.append('pilot', 'Master');
        url.searchParams.append('drone_name', 'Master');
        url.searchParams.append('datetime', new Date().toISOString());
        
        if (currentSearch) {
          url.searchParams.append('property_name', currentSearch);
        }

        console.log('Fetching URL:', url.toString());

        const res = await fetch(url.toString(), {
          method: 'GET',
          headers: {
            'x-api-key': API_KEY
          }
        });

        console.log('Response status:', res.status);

        if (!res.ok) {
          throw new Error(`HTTP error! status: ${res.status}`);
        }

        const response = await res.json();
        console.log('Got response:', response);

        const surveys = response.data || [];
        const pagination = response.pagination || {};

        // Update pagination state
        hasMoreResults = pagination.hasNextPage || false;
        totalResults = pagination.totalRecords || 0;

        // Update results info
        updateResultsInfo(pagination);

        if (surveys.length === 0) {
          if (currentPage === 1) {
            document.getElementById('no-results').style.display = 'block';
          } else {
            document.getElementById('end-of-results').style.display = 'block';
          }
        } else {
          renderSurveys(surveys);
          currentPage++;
        }

        if (!hasMoreResults && surveys.length > 0) {
          document.getElementById('end-of-results').style.display = 'block';
        }

      } catch (err) {
        console.error('Error fetching archives:', err);
        
        if (err.message.includes('Failed to fetch')) {
          showError('Network error: Unable to connect to the server. Please check your connection and try again.');
        } else if (err.message.includes('HTTP error')) {
          showError(`Server error: ${err.message}`);
        } else {
          showError('Error loading archived surveys. Please try again.');
        }
      } finally {
        setLoadingState(false);
      }
    }

    function updateResultsInfo(pagination) {
      const resultsDiv = document.getElementById('results-info');
      if (pagination.totalRecords > 0) {
        const searchText = currentSearch ? ` matching "${currentSearch}"` : '';
        resultsDiv.textContent = `Found ${pagination.totalRecords} archived survey${pagination.totalRecords === 1 ? '' : 's'}${searchText}`;
        resultsDiv.style.display = 'block';
      } else {
        resultsDiv.style.display = 'none';
      }
    }

    async function renderSurveys(surveys) {
      console.log('Rendering', surveys.length, 'archived surveys');
      const container = document.getElementById('surveys-container');

      for (let i = 0; i < surveys.length; i++) {
        const survey = surveys[i];
        console.log('Rendering archived survey:', survey);

        const card = document.createElement('div');
        card.className = 'survey-card';

        // Generate unique ID for this survey
        const surveyId = `archived_survey_${currentPage}_${i}`;
        surveyDataStore[surveyId] = survey;

        // Process image data
        let imageHtml = '';
        if (survey.image) {
          console.log('Survey has image data:', typeof survey.image);
          const imageSrc = processImageData(survey.image);
          
          if (imageSrc) {
            imageHtml = `
              <div class="survey-image">
                <img src="${imageSrc}" alt="Survey Image" 
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block';" />
                <div class="image-error" style="display: none;">Image failed to load</div>
              </div>
            `;
          } else {
            imageHtml = `
              <div class="survey-image">
                <div class="image-error">Unable to process image data</div>
              </div>
            `;
          }
        }

        // Process PDF data
        let pdfHtml = '';
        if (survey.pdf) {
          console.log('Survey has PDF data:', typeof survey.pdf);
          const pdfSrc = processPdfData(survey.pdf);
          
          if (pdfSrc) {
            const pdfId = `pdf-${surveyId}`;
            pdfHtml = `
              <div class="pdf-section">
                <div class="pdf-viewer" id="${pdfId}-viewer">
                  <iframe src="${pdfSrc}#toolbar=0&navpanes=0&scrollbar=0" 
                          title="Survey PDF" 
                          onerror="showPdfError('${pdfId}')">
                  </iframe>
                </div>
              </div>
            `;
            
            // Store PDF data for modal viewing
            window[`pdfData_${pdfId}`] = {
              src: pdfSrc,
              title: survey.property_name || 'Archived Survey'
            };
          } else {
            pdfHtml = `
              <div class="pdf-section">
                <div class="pdf-error">
                  <div class="pdf-icon" style="width: 40px; height: 40px; font-size: 16px; margin-bottom: 10px;">PDF</div>
                  <div>Unable to process PDF data</div>
                </div>
              </div>
            `;
          }
        }

        // Combine attachments
        let attachmentsHtml = '';
        if (imageHtml || pdfHtml) {
          attachmentsHtml = `
            <div class="survey-attachments">
              ${imageHtml}
              ${pdfHtml}
            </div>
          `;
        }

        // Format archived date
        const archivedDate = new Date(survey.archived_at).toLocaleString();
        const originalDate = new Date(survey.scheduled_time).toLocaleString();

        card.innerHTML = `
          <div class="survey-info">
            <p><strong>Original ID:</strong> ${survey.original_id || ''}<span class="archived-badge">ARCHIVED</span></p>
            <p><strong>Pilot:</strong> ${survey.pilot || ''}</p>
            <p><strong>Drone:</strong> ${survey.drone_name || ''}</p>
            <p><strong>Property Name:</strong> ${survey.property_name || ''}</p>
            <p><strong>Original Scheduled Time:</strong> ${originalDate}</p>
            <p><strong>Archived On:</strong> ${archivedDate}</p>
            <p><strong>Coordinates:</strong> ${survey.coordinates || ''}</p>
            <p><strong>Gate Code:</strong> ${survey.gate_code || ''}</p>
            <p><strong>Notes:</strong> ${survey.notes || ''}</p>
            <p><strong>Driving Instructions:</strong> ${survey.driving_instructions || ''}</p>
            <p><strong>Google Sheets ID:</strong> ${survey.google_sheets_id || ''}</p>
          </div>
          ${attachmentsHtml}
        `;

        container.appendChild(card);
      }
    }

    // PDF Modal Functions
    function openPdfModal(pdfSrc, title) {
      console.log('openPdfModal called with:', pdfSrc, title);
      
      let modal = document.getElementById('pdf-modal');
      if (!modal) {
        console.log('Creating new PDF modal');
        modal = document.createElement('div');
        modal.id = 'pdf-modal';
        modal.className = 'pdf-modal';
        modal.innerHTML = `
          <div class="pdf-modal-content">
            <div class="pdf-modal-header">
              <h3 id="pdf-modal-title">PDF Viewer</h3>
              <button class="pdf-modal-close" onclick="closePdfModal()">&times;</button>
            </div>
            <iframe id="pdf-modal-frame" src="" title="PDF Viewer"></iframe>
          </div>
        `;
        document.body.appendChild(modal);
        
        modal.addEventListener('click', function(e) {
          if (e.target === modal) {
            closePdfModal();
          }
        });
        
        document.addEventListener('keydown', function(e) {
          if (e.key === 'Escape' && modal.style.display === 'block') {
            closePdfModal();
          }
        });
      }
      
      document.getElementById('pdf-modal-title').textContent = `${title} - PDF Viewer`;
      document.getElementById('pdf-modal-frame').src = pdfSrc + '#toolbar=1';
      modal.style.display = 'block';
      document.body.style.overflow = 'hidden';
      
      console.log('PDF modal should now be visible');
    }

    function closePdfModal() {
      console.log('Closing PDF modal');
      const modal = document.getElementById('pdf-modal');
      if (modal) {
        modal.style.display = 'none';
        document.getElementById('pdf-modal-frame').src = '';
        document.body.style.overflow = 'auto';
      }
    }

    function handlePdfClick(pdfId) {
      console.log('PDF clicked:', pdfId);
      const pdfData = window[`pdfData_${pdfId}`];
      
      if (pdfData) {
        console.log('Opening PDF modal with:', pdfData.src, pdfData.title);
        openPdfModal(pdfData.src, pdfData.title);
      } else {
        console.error('PDF data not found for:', pdfId);
        alert('Error: PDF data not found');
      }
    }

    function showPdfError(pdfId) {
      const viewer = document.getElementById(`${pdfId}-viewer`);
      if (viewer) {
        const pdfData = window[`pdfData_${pdfId}`];
        
        viewer.innerHTML = `
          <div class="pdf-error" onclick="handlePdfClick('${pdfId}')" title="Click to view PDF in fullscreen">
            <div class="pdf-icon" style="width: 40px; height: 40px; font-size: 16px; margin-bottom: 10px;">PDF</div>
            <div>PDF could not be displayed</div>
            <div style="font-size: 12px; margin-top: 5px;">Click to view in fullscreen</div>
          </div>
        `;
        viewer.style.cursor = 'pointer';
      }
    }

    // Utility functions for processing image and PDF data (same as original)
    function processImageData(imageData) {
      console.log("Processing image data:", typeof imageData, imageData);
      
      if (!imageData) {
        console.log("No image data provided");
        return null;
      }
      
      if (typeof imageData === 'string' && imageData.startsWith('data:')) {
        console.log("Image is already a data URL");
        return imageData;
      }
      
      if (typeof imageData === 'string' && !imageData.startsWith('data:')) {
        console.log("Image is base64 string, adding data URL prefix");
        const isPNG = imageData.startsWith('iVBORw0KGgo');
        const mimeType = isPNG ? 'image/png' : 'image/jpeg';
        return `data:${mimeType};base64,${imageData}`;
      }
      
      if (imageData && typeof imageData === 'object' && imageData.type === 'Buffer' && Array.isArray(imageData.data)) {
        console.log("Processing Buffer object with data array");
        try {
          const uint8Array = new Uint8Array(imageData.data);
          const base64 = uint8ArrayToBase64(uint8Array);
          
          const isPNG = uint8Array[0] === 0x89 && uint8Array[1] === 0x50 && uint8Array[2] === 0x4E && uint8Array[3] === 0x47;
          const isJPEG = uint8Array[0] === 0xFF && uint8Array[1] === 0xD8;
          
          let mimeType = 'image/png';
          if (isJPEG) {
            mimeType = 'image/jpeg';
          } else if (isPNG) {
            mimeType = 'image/png';
          }
          
          console.log("Detected image type:", mimeType);
          return `data:${mimeType};base64,${base64}`;
        } catch (error) {
          console.error("Error processing Buffer object:", error);
          return null;
        }
      }
      
      if (imageData instanceof ArrayBuffer) {
        console.log("Processing ArrayBuffer");
        try {
          const uint8Array = new Uint8Array(imageData);
          const base64 = uint8ArrayToBase64(uint8Array);
          
          const isPNG = uint8Array[0] === 0x89 && uint8Array[1] === 0x50 && uint8Array[2] === 0x4E && uint8Array[3] === 0x47;
          const isJPEG = uint8Array[0] === 0xFF && uint8Array[1] === 0xD8;
          
          let mimeType = 'image/png';
          if (isJPEG) {
            mimeType = 'image/jpeg';
          } else if (isPNG) {
            mimeType = 'image/png';
          }
          
          return `data:${mimeType};base64,${base64}`;
        } catch (error) {
          console.error("Error processing ArrayBuffer:", error);
          return null;
        }
      }
      
      if (imageData instanceof Uint8Array) {
        console.log("Processing Uint8Array");
        try {
          const base64 = uint8ArrayToBase64(imageData);
          
          const isPNG = imageData[0] === 0x89 && imageData[1] === 0x50 && imageData[2] === 0x4E && imageData[3] === 0x47;
          const isJPEG = imageData[0] === 0xFF && imageData[1] === 0xD8;
          
          let mimeType = 'image/png';
          if (isJPEG) {
            mimeType = 'image/jpeg';
          } else if (isPNG) {
            mimeType = 'image/png';
          }
          
          return `data:${mimeType};base64,${base64}`;
        } catch (error) {
          console.error("Error processing Uint8Array:", error);
          return null;
        }
      }
      
      if (Array.isArray(imageData)) {
        console.log("Processing array as byte array");
        try {
          const uint8Array = new Uint8Array(imageData);
          const base64 = uint8ArrayToBase64(uint8Array);
          
          const isPNG = uint8Array[0] === 0x89 && uint8Array[1] === 0x50 && uint8Array[2] === 0x4E && uint8Array[3] === 0x47;
          const isJPEG = uint8Array[0] === 0xFF && uint8Array[1] === 0xD8;
          
          let mimeType = 'image/png';
          if (isJPEG) {
            mimeType = 'image/jpeg';
          } else if (isPNG) {
            mimeType = 'image/png';
          }
          
          return `data:${mimeType};base64,${base64}`;
        } catch (error) {
          console.error("Error processing array:", error);
          return null;
        }
      }
      
      console.warn("Unknown image data format:", typeof imageData);
      return null;
    }

    function processPdfData(pdfData) {
      console.log("Processing PDF data:", typeof pdfData, pdfData);
      
      if (!pdfData) {
        console.log("No PDF data provided");
        return null;
      }
      
      if (typeof pdfData === 'string' && pdfData.startsWith('data:')) {
        console.log("PDF is already a data URL");
        return pdfData;
      }
      
      if (typeof pdfData === 'string' && !pdfData.startsWith('data:')) {
        console.log("PDF is base64 string, adding data URL prefix");
        return `data:application/pdf;base64,${pdfData}`;
      }
      
      if (pdfData && typeof pdfData === 'object' && pdfData.type === 'Buffer' && Array.isArray(pdfData.data)) {
        console.log("Processing PDF Buffer object with data array");
        try {
          const uint8Array = new Uint8Array(pdfData.data);
          const base64 = uint8ArrayToBase64(uint8Array);
          return `data:application/pdf;base64,${base64}`;
        } catch (error) {
          console.error("Error processing PDF Buffer object:", error);
          return null;
        }
      }
      
      if (pdfData instanceof ArrayBuffer) {
        console.log("Processing PDF ArrayBuffer");
        try {
          const uint8Array = new Uint8Array(pdfData);
          const base64 = uint8ArrayToBase64(uint8Array);
          return `data:application/pdf;base64,${base64}`;
        } catch (error) {
          console.error("Error processing PDF ArrayBuffer:", error);
          return null;
        }
      }
      
      if (pdfData instanceof Uint8Array) {
        console.log("Processing PDF Uint8Array");
        try {
          const base64 = uint8ArrayToBase64(pdfData);
          return `data:application/pdf;base64,${base64}`;
        } catch (error) {
          console.error("Error processing PDF Uint8Array:", error);
          return null;
        }
      }
      
      if (Array.isArray(pdfData)) {
        console.log("Processing PDF array as byte array");
        try {
          const uint8Array = new Uint8Array(pdfData);
          const base64 = uint8ArrayToBase64(uint8Array);
          return `data:application/pdf;base64,${base64}`;
        } catch (error) {
          console.error("Error processing PDF array:", error);
          return null;
        }
      }
      
      console.warn("Unknown PDF data format:", typeof pdfData);
      return null;
    }

    function uint8ArrayToBase64(uint8Array) {
      const CHUNK_SIZE = 0x8000; // 32KB chunks to avoid call stack overflow
      let result = '';
      
      for (let i = 0; i < uint8Array.length; i += CHUNK_SIZE) {
        const chunk = uint8Array.subarray(i, i + CHUNK_SIZE);
        result += String.fromCharCode.apply(null, chunk);
      }
      
      return btoa(result);
    }
  </script>
</body>
</html>